import {Component, OnInit} from '@angular/core';
import {CompanyService} from "../../../../core/service/company.service";
import {Company} from "../../../../core/model/company";
import {BsModalRef} from "ngx-bootstrap/modal";
import {BarcodeSettingsService, BarcodeSettings, CostCodeSettings} from "../../../../core/service/barcode-settings.service";

@Component({
  selector: 'app-barcode',
  templateUrl: './barcode.component.html',
  styleUrls: ['./barcode.component.css']
})
export class BarcodeComponent implements OnInit {

  barcode = '';
  itemName = '';
  itemCode = '';
  price = '';
  itemCost = 0; // Item cost for cost code conversion
  // noOfCopies = 3; // This variable is no longer directly used from input. We'll use numberOfStickersToPrint.
  numberOfStickersToPrint: number = 1; // New variable to hold the total number of stickers the user wants to print. Default to 1.
  numberOfColumns = 2; // Add columns control
  elementType = 'svg';
  format = 'CODE128';
  lineColor = '#000000';
  width = 0.8;
  height = 18;
  displayValue = false;
  fontOptions = '';
  font = 'monospace';
  textAlign = 'center';
  textPosition = 'bottom';
  textMargin = 0;
  fontSize = 9;
  background = '#ffffff';
  margin = 0;
  marginTop = 0;
  marginBottom = 0;
  marginLeft = 0;
  marginRight = 0;

  // Settings
  barcodeSettings: BarcodeSettings;
  costCodeSettings: CostCodeSettings;
  useCostCodes = false;
  showBarcode = true;
  paperSize = '30x20';

  // Make Math available in template
  Math = Math;

  company: Company;
  modalRef: BsModalRef;

  constructor(
    private companyService: CompanyService,
    private barcodeSettingsService: BarcodeSettingsService
  ) {
  }

  ngOnInit() {
    this.findCompany();
    this.company = new Company();
    this.loadSettings();

    // Debug log to check if properties are set
    console.log('Barcode component initialized with:', {
      barcode: this.barcode,
      itemName: this.itemName,
      price: this.price,
      itemCode: this.itemCode
    });
  }

  findCompany() {
    this.companyService.findCompany().subscribe((data: Company) => {
      this.company = data;
    });
  }

  /**
   * Load barcode and cost code settings
   */
  loadSettings() {
    // Load barcode settings from general settings
    this.barcodeSettings = this.barcodeSettingsService.getBarcodeSettingsFromGeneralSettings();
    this.useCostCodes = this.barcodeSettings.useCostCodes;
    this.showBarcode = this.barcodeSettings.showBarcode;
    this.numberOfColumns = this.barcodeSettings.columns;
    this.paperSize = this.barcodeSettings.paperSize;
    // Initialize numberOfStickersToPrint based on columns for a single row preview
    this.numberOfStickersToPrint = this.numberOfColumns;


    // Load cost code settings
    this.barcodeSettingsService.getCostCodeSettings().subscribe(settings => {
      this.costCodeSettings = settings;
    });
  }

  /**
   * Get the display code for preview (with cost code conversion if enabled)
   */
  getDisplayCode(): string {
    if (this.useCostCodes && this.itemCost > 0) {
      // Convert item cost to cost code letters
      const costNumber = Math.round(this.itemCost * 100); // Convert to cents to avoid decimals
      const letters = this.barcodeSettingsService.convertNumberToCostCodeLetters(costNumber);
      return letters;
    }
    return this.barcode;
  }

  /**
   * Get the barcode value for ngx-barcode element (should always be the original barcode for scanning)
   */
  getBarcodeValue(): string {
    return this.barcode;
  }

  /**
   * Calculate the number of rows that will be printed
   */
  getCalculatedRows(): number {
    const totalStickersToPrint = Math.max(1, Math.floor(this.numberOfStickersToPrint || 1));
    const fullRows = Math.floor(totalStickersToPrint / this.numberOfColumns);
    const remainderStickers = totalStickersToPrint % this.numberOfColumns;
    return remainderStickers === 0 ? fullRows : fullRows + 1;
  }

  /**
   * Handle sticker count change to trigger preview update
   */
  onStickerCountChange(): void {
    // Ensure the value is at least 1
    if (this.numberOfStickersToPrint < 1) {
      this.numberOfStickersToPrint = 1;
    }
    // The preview will automatically update due to the getPreviewItems() method being called
  }

  /**
   * Get array for rows iteration (like the original table approach)
   */
  getRowsArray(): number[] {
    const totalRows = this.getCalculatedRows();
    return Array(totalRows).fill(0).map((x, i) => i);
  }

  /**
   * Get array for columns iteration (like the original table approach)
   */
  getColumnsArray(): number[] {
    return Array(this.numberOfColumns).fill(0).map((x, i) => i);
  }

  /**
   * Generate preview items array - show the actual number of stickers that will be printed
   * Calculate rows based on numberOfStickersToPrint and numberOfColumns
   */
  getPreviewItems(): any[] {
    const items = [];
    const totalStickersToPrint = Math.max(1, Math.floor(this.numberOfStickersToPrint || 1));

    // Calculate the number of full rows required
    const fullRows = Math.floor(totalStickersToPrint / this.numberOfColumns);
    // Calculate the number of stickers in the last (possibly partial) row
    const remainderStickers = totalStickersToPrint % this.numberOfColumns;

    let actualRowsToPrint: number;
    if (remainderStickers === 0) {
      // If no remainder, we print exactly the full rows
      actualRowsToPrint = fullRows;
    } else {
      // If there's a remainder, we need one extra row for those stickers
      actualRowsToPrint = fullRows + 1;
    }

    // Generate items for all rows that will be printed
    const itemsToGenerate = actualRowsToPrint * this.numberOfColumns;

    for (let i = 0; i < itemsToGenerate; i++) {
      items.push({
        barcode: this.barcode,
        displayCode: this.getDisplayCode(),
        itemName: this.itemName || 'No Name',
        price: this.price || '0.00'
      });
    }
    return items;
  }

  /**
   * Get sticker dimensions based on paper size
   */
  getStickerDimensions(): { width: string, height: string } {
    const dimensions = {
      '30x20': { width: '30mm', height: '20mm' },
      '33x21': { width: '33mm', height: '21mm' },
      '38x25': { width: '38mm', height: '25mm' },
      '50x25': { width: '50mm', height: '25mm' },
      '65x15': { width: '65mm', height: '15mm' },
      '100x50': { width: '100mm', height: '50mm' },
      '100x150': { width: '100mm', height: '150mm' }
    };
    return dimensions[this.paperSize] || dimensions['30x20'];
  }

  /**
   * Get container styles for the preview grid
   */
  getContainerStyles(): string {
    const dimensions = this.getStickerDimensions();
    const totalStickersToPrint = Math.max(1, Math.floor(this.numberOfStickersToPrint || 1));

    // Calculate the number of rows needed
    const fullRows = Math.floor(totalStickersToPrint / this.numberOfColumns);
    const remainderStickers = totalStickersToPrint % this.numberOfColumns;
    const actualRowsToPrint = remainderStickers === 0 ? fullRows : fullRows + 1;

    // Calculate container width for flexbox
    const gapSize = 5; // 5mm gap
    const stickerWidthNum = parseInt(dimensions.width.replace('mm', ''));
    const containerWidth = (stickerWidthNum * this.numberOfColumns) + (gapSize * (this.numberOfColumns - 1));

    return `
      display: flex;
      flex-wrap: wrap;
      width: ${containerWidth}mm;
      gap: ${gapSize}mm;
      padding: 10mm;
      justify-content: flex-start;
      align-content: flex-start;
      margin: 0 auto;
      border: 2px dashed #ccc;
      background-color: #f9f9f9;
      box-sizing: border-box;
    `;
  }

  /**
   * Get individual sticker styles
   */
  getStickerStyles(): string {
    const dimensions = this.getStickerDimensions();
    return `
      border: none !important;
      outline: none !important;
      box-shadow: none !important;
      padding: 0.5mm;
      padding-top: 2.5mm;
      text-align: center;
      background-color: transparent;
      position: relative;
      width: ${dimensions.width};
      height: ${dimensions.height};
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      box-sizing: border-box;
      overflow: hidden;
      flex-shrink: 0;
      page-break-inside: avoid;
    `;
  }

  /**
   * Get dynamic font size for item name based on its length
   */
  getItemNameFontSize(): string {
    const nameLength = (this.itemName || '').length;
    const paperSize = this.paperSize;

    // Base font sizes for different paper sizes
    const baseSizes = {
      '30x20': { short: '0.7em', medium: '0.6em', long: '0.5em' },
      '33x21': { short: '0.75em', medium: '0.65em', long: '0.55em' },
      '38x25': { short: '0.8em', medium: '0.7em', long: '0.6em' },
      '50x25': { short: '0.85em', medium: '0.75em', long: '0.65em' },
      '65x15': { short: '0.6em', medium: '0.5em', long: '0.45em' }, // Narrow stickers
      '100x50': { short: '1.0em', medium: '0.9em', long: '0.8em' },
      '100x150': { short: '1.2em', medium: '1.0em', long: '0.9em' }
    };

    const sizes = baseSizes[paperSize] || baseSizes['30x20'];

    // Determine size based on name length
    if (nameLength <= 10) {
      return sizes.short;  // Short names get larger text
    } else if (nameLength <= 20) {
      return sizes.medium; // Medium names get medium text
    } else {
      return sizes.long;   // Long names get smaller text
    }
  }

  /**
   * Get responsive font sizes based on sticker size
   */
  getResponsiveFontSizes(): { code: string, name: string, price: string, barcode: string } {
    // Font sizes optimized for each sticker size
    const fontSizes = {
      '30x20': { code: 8, name: 6, price: 7, barcode: 6 },
      '33x21': { code: 9, name: 7, price: 8, barcode: 7 },
      '38x25': { code: 10, name: 8, price: 9, barcode: 8 },
      '50x25': { code: 12, name: 10, price: 11, barcode: 10 },
      '65x15': { code: 8, name: 6, price: 7, barcode: 6 },
      '100x50': { code: 14, name: 12, price: 13, barcode: 12 },
      '100x150': { code: 18, name: 16, price: 17, barcode: 16 }
    };

    const sizes = fontSizes[this.paperSize] || fontSizes['30x20'];

    return {
      code: `${sizes.code}pt`,
      name: `${sizes.name}pt`,
      price: `${sizes.price}pt`,
      barcode: `${sizes.barcode}pt`
    };
  }

  print() {
    // Ensure numberOfStickersToPrint is a valid number and at least 1
    const totalStickersToPrint = Math.max(1, Math.floor(this.numberOfStickersToPrint));

    // Calculate the number of full rows required
    const fullRows = Math.floor(totalStickersToPrint / this.numberOfColumns);
    // Calculate the number of stickers in the last (possibly partial) row
    const remainderStickers = totalStickersToPrint % this.numberOfColumns;

    let actualRowsToPrint: number;
    if (remainderStickers === 0) {
      // If no remainder, we print exactly the full rows
      actualRowsToPrint = fullRows;
    } else {
      // If there's a remainder, we need one extra row for those stickers
      // and we will fill the rest of that row with "extra" stickers as per your requirement.
      actualRowsToPrint = fullRows + 1;
    }

    // Determine the total number of items to generate for printing.
    // This will be actualRowsToPrint * numberOfColumns to fill all rows completely for printing.
    const itemsToGenerate = actualRowsToPrint * this.numberOfColumns;

    const items = [];
    for (let i = 0; i < itemsToGenerate; i++) { // Generate items based on the calculated total needed for full rows
      items.push({
        barcode: this.barcode,
        itemCode: this.itemCode,
        itemName: this.itemName,
        sellingPrice: parseFloat(this.price) || 0,
        itemCost: this.itemCost
      });
    }

    // Generate HTML using the barcode settings service with specified number of rows
    const printHTML = this.barcodeSettingsService.generateBarcodeHTML(items, actualRowsToPrint); // Pass actualRowsToPrint here

    // Open in new window and print
    const printWindow = window.open('', '_blank', 'width=800,height=600');
    if (printWindow) {
      printWindow.document.write(printHTML);
      printWindow.document.close();
      printWindow.focus();

      // Add a small delay before printing to ensure content is loaded
      setTimeout(() => {
        printWindow.print();
        printWindow.close();
      }, 500);
    } else {
      alert('Please allow popups for this site to enable printing.');
    }
  }

}
